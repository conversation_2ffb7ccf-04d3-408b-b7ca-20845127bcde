<%--
  - ファイル名 : rbs22003_nohin_toroku.jsp
  - タイトル   : 納品登録画面
  - 説明       : 納品登録画面を出力します。
  - 著作権     : Copyright (c) 
  - 会社名     : Vinculum Japan Corporation
  - Author <PERSON><PERSON>
  - Version 1.00 2024/11/01 VINX redmine#29917_不要コメント削除
  - Version 1.01 2025/09/23 VINX redmine#XXXXX_UI改善対応
--%>

<%@page contentType="text/html; charset=windows-31j" %>

<%@ page import="java.net.URLEncoder" %>
<%@ page import="java.util.Iterator" %>
<%@ page import="java.math.BigDecimal" %>
<%@ page import="jp.co.vinculumjapan.stc.util.calendar.DateChanger" %>
<%@ page import="jp.co.vinculumjapan.stc.util.infostring.InfoStrings" %>
<%@ page import="mdware.seisenedi.common.util.HTMLUtil" %>
<%@ page import="mdware.seisenedi.common.util.SystemControlUtil" %>
<%@ page import="mdware.seisenedi.edi.exec_order.bean.NohinTorokuBean" %>
<%@ page import="mdware.seisenedi.edi.common.bean.WkGenkaCheckBean" %>
<jsp:useBean id="jutyuItiranStatus" scope="session" class="mdware.seisenedi.edi.exec_order.bean.JutyuItiranStatus" />
<jsp:useBean id="jutyuItiranBean" scope="session" class="mdware.seisenedi.edi.exec_order.bean.JutyuItiranNoMarketBean" />
<jsp:useBean id="jutyuTorokuBh"   scope="session" class="jp.co.vinculumjapan.stc.util.bean.BeanHolder" />
<jsp:useBean id="wkGenkaCheck"    scope="session" class="java.util.HashMap" />

<%
	// 納品単価訂正可能フラグ設定
	boolean tankaChgFg = false;	
	// 非相場商品、一括仕入伝票作成フラグ対象の場合、単価変更不可能
	if( jutyuItiranBean.isHiSoba() || jutyuItiranBean.isIkkatuSiireDenpFg() == true ){
		tankaChgFg = false;
	}else{
		tankaChgFg = true;
	}

	// JSP で InfoStrings を利用できるようにします。
	InfoStrings infoStrings = InfoStrings.getInstance();
%>


<html>
<head>
<title>納品登録</title>
<meta http-equiv="Content-Type" content="text/html; charset=Shift_JIS">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Content-Style-Type" content="text/css">
<link rel="stylesheet" href="./css/common.css" type="text/css">
<script language="JavaScript" type="text/javascript" src="./js/mdware_calc_util.js"></script>
<script language="JavaScript" type="text/javascript" src="./js/mdware_common.js"></script>
<script language="JavaScript" type="text/javascript" src="./js/mdware_date.js"></script>
<script language="JavaScript" type="text/javascript" src="./js/mdware_sub_search.js"></script>
<style type="text/css">
	div.list1 {
		width: 1340px;
	}
	
	div#result_scroll {
		overflow: scroll;
		overflow-x: hidden;
		height: 390px;
	}
	
	table.list1_search {
		width: 1325px;
	}
	
	table.list1_search th {
		box-sizing: border-box;
		padding: 0 2px;
	}
	
	table.list1_search td {
		box-sizing: border-box;
		padding: 0 2px;
	}
	
	table.list1_search td input[type="text"] {
		width: calc(100% - 4px);
	}
	
	table.list1_search td[class$="qt"] input[type="text"]{
		text-align: right;
	}
	
	table.list1_search td[class$="vl"] input[type="text"] {
		text-align: right;
	}
	
	table.list1_search td[class$="tanka"] input[type="text"]{
		text-align: right;
	}
	
	table.none_border * {
		border: none !important;
	}
	
	/* 空白列 */
	th.list1_no {
		width: 30px;
	}
	
	td.list1_no {
		width: 30px;
		text-align: center;
	}
	
	/* 商品情報ヘッダー */
	th.list1_syohin_info {
		width: 320px;
		padding-left: 4px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_syohin_info {
		width: 320px;
		padding-left: 4px;
		white-space: nowrap;
		text-align: center;
	}
	
	/* 商品コード */
	th.list1_syohin_cd {
		width: 175px;
		white-space: nowrap;
		text-align: left;
	}
	
	td.list1_syohin_cd {
		width: 175px;
		white-space: nowrap;
		text-align: left;
	}
	
	td.list1_syohin_cd  input[type="text"] {
		text-align: left;
	}
	
	/* 入数 */
	th.list1_iritsu {
		width: 80px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_iritsu {
		width: 80px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_iritsu  input[type="text"] {
		text-align: right;
	}
	
	/* 発単 */
	th.list1_hachi_tani_na {
		width: 50px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachi_tani_na {
		width: 50px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachi_tani_na input[type="text"] {
		text-align: center;
	}
	
	/* 商品名 */
	th.list1_syohin_na {
		width: 100%;
		white-space: nowrap;
		text-align: left;
	}
	
	td.list1_syohin_na {
		width: 100%;
		white-space: nowrap;
		text-align: left;
	}
	
	td.list1_syohin_na input[type="text"] {
		text-align: left;
	}
	
	/* 発注数 */
	th.list1_hachu_qt {
		width: 65px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachu_qt {
		width: 65px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachu_qt input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 発注数量 */
	th.list1_hachu_suryo_qt {
		width: 105px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachu_suryo_qt {
		width: 105px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_hachu_suryo_qt input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 訂正後納品数 */
	th.list1_kakutei_qt {
		width: 110px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_kakutei_qt {
		width: 110px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_kakutei_qt input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 訂正後納品数量 */
	th.list1_kakutei_suryo {
		width: 125px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_kakutei_suryo {
		width: 125px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_kakutei_suryo input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 原単価 */
	th.list1_gentanka {
		width: 90px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_gentanka {
		width: 90px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_gentanka input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 売単価 */
	th.list1_baitanka {
		width: 70px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_baitanka {
		width: 70px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_baitanka input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 産地 */
	th.list1_santi {
		width: 130px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_santi {
		width: 130px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_santi input[type="text"][name^="kaku_santi_na_"] {
		width: calc(100% - 65px);
		text-align: left;
	}
	
	td.list1_santi input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 等級 */
	th.list1_tokaikyu {
		width: 130px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_tokaikyu {
		width: 130px;
		white-space: nowrap;
		text-align: center;
	}
	
	td.list1_tokaikyu input[type="text"][name^="kaku_tokaikyu_na_"] {
		width: calc(100% - 65px);
		text-align: left;
	}
	
	td.list1_tokaikyu input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 規格 */
	th.list1_kikaku {
		width: 130px;
	}
	
	td.list1_kikaku {
		width: 130px;
	}
	
	td.list1_kikaku input[type="text"][name^="kaku_kikaku_na_"] {
		width: calc(100% - 65px);
		text-align: left;
	}
	
	td.list1_kikaku input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 売価金額合計 */
	th.list1_baika_kei {
		width: 140px;
	}
	
	td.list1_baika_kei {
		width: 140px;
	}
	
	td.list1_baika_kei input {
		width: calc(100% - 4px);
		text-align: right;
	}
	
	td.list1_baika_kei input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
	
	/* 原価金額合計 */
	th.list1_genka_kei {
		width: 140px;
	}
	
	td.list1_genka_kei {
		width: 140px;
	}
	
	td.list1_genka_kei input {
		width: calc(100% - 4px);
		text-align: right;
	}
	
	td.list1_genka_kei input[readonly] {
		background-color: #ededed !important;
		border-color: #a7a7a7 !important;
	}
</style>
<script language="JavaScript" type="text/JavaScript">
	<jsp:include page="rbs00000_common_onload_js.jsp" flush="true" />
	
	// 確定数チェックフラグ（フォーカス制御用）
	var meisaiKakuteiQtChekFg = "false";
	
	<%-- 初期処理 --%>
	function init() {
		document.MainForm.term_zitu_nohin_dt.focus();
	}
	
	<%-- 受注一覧(戻る)押下 --%>
	function returnCommand() {
	
		// 入力内容変更チェックを行います。
		modified();
	
		// 入力内容に変更がある場合、ユーザ確認を行います。
		if( document.MainForm.Modified.value == 1 ) {
			if( !confirm("<%= infoStrings.getInfo("0033") %>") ) {
				return;
			}
		}
	
		doTransaction('jutyuItiranReturn');
	}
	
	<%-- 登録押下 --%>
	function updateCommand() {
	
		// 明細行の変更チェック
		modified();	// この名前の関数で、更新内容を変更したかどうかという処理を各画面で記述。
		if(document.MainForm.Modified.value != '1'){
			alert('<%= infoStrings.getInfo("32156") %>');
	 		return;
		}
	
		// 画面ごとの更新内容のチェックを行います。
		if( checkInput() == false ) {
			return;
		}
	
		if( confirm("<%= infoStrings.getInfo("0003") %>") ) {
			doTransaction('nohinTorokuUpdate');
		}
	}
	
	<%-- 更新項目チェック用関数 --%>
	function checkInput() {
	
		// 入力値のチェックを行い、警告を一度だけ出すためのフラグ。
		var CHK_1 = false;
		var CHK_3 = false;
		var CHK_4 = false;
		var CHK_5 = false;
	
		// 実納品日
		if( isTextCheck("実納品日", MainForm.term_zitu_nohin_dt, 1, 1, 8) == false ) {
			return false;
		}
		// 実納品日
		if( dateFormalizer(MainForm.term_zitu_nohin_dt) == false ) {
			return false;
		}
		// 発注日＞納品日の場合エラー。
		if( Number(<%=jutyuItiranBean.getHachuDt()%>) > Number(MainForm.term_zitu_nohin_dt.value) ) {
			alert("<%= infoStrings.getInfo("32050").replaceAll("%hachu_dt%", DateChanger.toSeparatorDate(jutyuItiranBean.getHachuDt())) %>");
			document.MainForm.term_zitu_nohin_dt.focus();
			return false;
		}
		// 原価のチェックを行い、警告を一度だけ出すためのフラグ。
		var warningFg1 = false;	// 原価＞＝５０万円
		var warningFg2 = false;	// 訂正後納品数量＞０かつ原価＜１０円
		var warningFg3 = false; // 訂正後納品数量＞０かつ原価＜原単価×0.5
		
		for( var i = 0; i < <%= jutyuTorokuBh.getMaxRows() %>; i++ ) {
		
			var obj1 = MainForm.all("kakutei_qt_" + i);
			var obj2 = MainForm.all("kakutei_suryo_qt_" + i);
			var obj3 = MainForm.all("kaku_gentanka_vl_" + i);
			var obj4 = MainForm.all("kaku_baitanka_vl_" + i);
			var obj5 = MainForm.all("ido_gentanka_vl_" + i);
			var obj6 = MainForm.all("ido_baitanka_vl_" + i);
			var isTeikan = MainForm.all("isTeikan_" + i);		
	
			// 納品数
			if( isTextCheck("納品数", obj1, 1, 0, 0) == false || isLengthCheck("納品数", obj1, 4, 0) == false ) {
				return false;
			}
			// 納品数量
			if( isTextCheck("納品数量", obj2, 4, 0, 0) == false || isLengthCheck("納品数量", obj2, 5, 2) == false ) {
				if( isTeikan.value == "true" ){
					obj1.focus();
					obj1.select();
				}		
				return false;
			}
			
		<%	if( tankaChgFg == true ) {	%>
		
		
				// 原単価
				if( isTextCheck("原単価", obj3, 4, 1, 0) == false || isLengthCheck("原単価", obj3, 7, 2) == false ) {
					return false;
				}
				// 売単価
				if( isTextCheck("売単価", obj4, 1, 1, 0) == false || isLengthCheck("売単価", obj4, 7, 0) == false ) {
					return false;
				}
				<%-- 原単価=0チェック --%>
				if( obj3.value == 0 ) {
					alert("<%= infoStrings.getInfo("32117") %>");
					obj3.focus();
					obj3.select();
					return false;
				}
				<%-- 原単価限度額チェック --%>
				if( obj3.value > 100000 ) {
					if( CHK_1 == false ){
						<%-- 原単価は1０万円以上に設定できません。 --%>
						if( !confirm("<%= InfoStrings.getInstance().getInfo("32116") %>") ) {
							obj3.focus();
							obj3.select();
							return false;
						}else{
							CHK_1 = true;
						}
					}
				}
				
				var num = new Number(obj4.value);	<%-- 売単価には小数点が付いていないためNumberオブジェクトに1度変換。 --%>
				if( obj4.value == 0 ) {	<%-- 売単価=0チェック(空欄の場合はチェックしない。) --%>
					if( CHK_3 == false ){
						<%-- 売単価が０円ですが、よろしいですか？ --%>
						if( !confirm("<%= InfoStrings.getInstance().getInfo("32118") %>") ) {
							obj4.focus();
							obj4.select();
							return false;
						}else{
							CHK_3 = true;
						}
					}
				}else if( obj3.value > num ) {	<%-- 原単価>売単価チェック --%>
					if( obj3.value != obj3.defaultValue || obj4.value != obj4.defaultValue ) {
						if( CHK_4 == false ){
							<%-- 売単価が原単価より小さいですが、よろしいですか？ --%>
							if( !confirm("<%= InfoStrings.getInstance().getInfo("32119") %>") ) {
								obj4.focus();
								obj4.select();
								return false;
							}else{
								CHK_4 = true;
							}
						}
					}
				} else if( obj3.value * 3 <= num ) {	<%-- 原単価*3<=売単価チェック --%>
					if( obj3.value != obj3.defaultValue || obj4.value != obj4.defaultValue ) {
						if( CHK_5 == false ){
							<%-- 売単価が原単価の３倍以上ですが、よろしいですか？ --%>
							if( !confirm("<%= InfoStrings.getInstance().getInfo("32120") %>") ) {
								obj4.focus();
								obj4.select();
								return false;
							}else{
								CHK_5 = true;
							}
						}
					}
				}
				
		<%	}	%>					
			
			// 原価売価の算出
			var suryo = 0;	// 使用する数量。(訂正後納品数量が空欄の場合、納品数量を使用する)
			if( MainForm["kakutei_suryo_qt_" + i] != null && MainForm["kakutei_suryo_qt_" + i].value != '' ) {
				suryo = MainForm["kakutei_suryo_qt_" + i].value;
			} else {
				suryo = MainForm["hachu_suryo_qt_" + i].value;
			}
			var genka_vl = calcGenkaVl(suryo, MainForm["gen_tan_tani_" + i].value, obj3.value, MainForm["teikan_kb_" + i].value);
			
			var baika_vl 	= calcBaikaVl("", suryo, "", obj4.value, "", "");
			var idogenka_vl = calcGenkaVl(suryo, MainForm["gen_tan_tani_" + i].value, obj5.value, MainForm["teikan_kb_" + i].value);
			var idobaika_vl = calcBaikaVl("", suryo, "", obj6.value, "", "");
			
			// 原価金額 11桁を超えるとエラー
			if( genka_vl >= 100000000000 ) {
				alert("原価金額（11桁）<%= infoStrings.getInfo("10005") %>");
				obj1.focus();
				obj1.select();
				return false;
			}
			// 売価金額 11桁を超えるとエラー
			if( baika_vl >= 100000000000 ) {
				alert("売価金額（11桁）<%= infoStrings.getInfo("10005") %>");
				obj1.focus();
				obj1.select();
				return false;
			}
			// 移動原価金額 11桁を超えるとエラー
			if( idogenka_vl >= 100000000000 ) {
				alert("移動原価金額（11桁）<%= infoStrings.getInfo("10005") %>");
				obj1.focus();
				obj1.select();
				return false;
			}
			// 移動売価金額 11桁を超えるとエラー
			if( idobaika_vl >= 100000000000 ) {
				alert("移動売価金額（11桁）<%= infoStrings.getInfo("10005") %>");
				obj1.focus();
				obj1.select();
				return false;
			}		
	
			// 原価のGEN01チェック
			if(MainForm.GEN01_FUTOGO.value == 'EQ'){
				if( genka_vl == Number(MainForm.GEN01_VALUE.value) ) {
					alert(MainForm.GEN01_MSG.value);
					return false;
				}
			}else if(MainForm.GEN01_FUTOGO.value == 'LT'){
				if( genka_vl < Number(MainForm.GEN01_VALUE.value) ) {
					alert(MainForm.GEN01_MSG.value);
					return false;
				}
			}else if(MainForm.GEN01_FUTOGO.value == 'LE'){
				if( genka_vl <= Number(MainForm.GEN01_VALUE.value) ) {
					alert(MainForm.GEN01_MSG.value);
					return false;
				}
			}else if(MainForm.GEN01_FUTOGO.value == 'GE'){
				if( genka_vl >= Number(MainForm.GEN01_VALUE.value) ) {
					alert(MainForm.GEN01_MSG.value);
					return false;
				}
			}else if(MainForm.GEN01_FUTOGO.value == 'GT'){
				if( genka_vl > Number(MainForm.GEN01_VALUE.value) ) {
					alert(MainForm.GEN01_MSG.value);
					return false;
				}
			}
	
			// 原価のGEN02チェック
			if(MainForm.GEN02_FUTOGO.value == 'EQ'){
				if( warningFg1 == false && genka_vl == Number(MainForm.GEN02_VALUE.value) ) {
					if( !confirm(MainForm.GEN02_MSG.value)){
						return false;
					} else {
						warningFg1 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN02_FUTOGO.value == 'LT'){
				if( warningFg1 == false && genka_vl < Number(MainForm.GEN02_VALUE.value) ) {
					if( !confirm(MainForm.GEN02_MSG.value)){
						return false;
					} else {
						warningFg1 = true;	// ２回目以降は警告を表示しない。
					}
					
				}
			}else if(MainForm.GEN02_FUTOGO.value == 'LE'){
				if( warningFg1 == false && genka_vl <= Number(MainForm.GEN02_VALUE.value) ) {
					if( !confirm(MainForm.GEN02_MSG.value)){
						return false;
					} else {
						warningFg1 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN02_FUTOGO.value == 'GE'){
				if( warningFg1 == false && genka_vl >= Number(MainForm.GEN02_VALUE.value) ) {
					if( !confirm(MainForm.GEN02_MSG.value)){
						return false;
					} else {
						warningFg1 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN02_FUTOGO.value == 'GT'){
				if( warningFg1 == false && genka_vl > Number(MainForm.GEN02_VALUE.value) ) {
					if( !confirm(MainForm.GEN02_MSG.value)){
						return false;
					} else {
						warningFg1 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}
	
			// 原価のGEN03チェック
			if(MainForm.GEN03_FUTOGO.value == 'EQ'){
				if( warningFg2 == false && suryo > 0 && genka_vl == Number(MainForm.GEN03_VALUE.value) ) {
					if( !confirm(MainForm.GEN03_MSG.value)){
						return false;
					} else {
						warningFg2 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN03_FUTOGO.value == 'LT'){
				if( warningFg2 == false && suryo > 0 && genka_vl < Number(MainForm.GEN03_VALUE.value) ) {
					if( !confirm(MainForm.GEN03_MSG.value)){
						return false;
					} else {
						warningFg2 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN03_FUTOGO.value == 'LE'){
				if( warningFg2 == false && suryo > 0 && genka_vl <= Number(MainForm.GEN03_VALUE.value) ) {
					if( !confirm(MainForm.GEN03_MSG.value)){
						return false;
					} else {
						warningFg2 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN03_FUTOGO.value == 'GE'){
				if( warningFg2 == false && suryo > 0 && genka_vl >= Number(MainForm.GEN03_VALUE.value) ) {
					if( !confirm(MainForm.GEN03_MSG.value)){
						return false;
					} else {
						warningFg2 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN03_FUTOGO.value == 'GT'){
				if( warningFg2 == false && suryo > 0 && genka_vl > Number(MainForm.GEN03_VALUE.value) ) {
					if( !confirm(MainForm.GEN03_MSG.value)){
						return false;
					} else {
						warningFg2 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}
	
			// 原価のGEN04チェック
			if(MainForm.GEN04_FUTOGO.value == 'EQ'){
				if( warningFg3 == false && suryo > 0 && genka_vl == demicalFloat(floor(MainForm["kaku_gentanka_vl_" + i].value, 0), Number(MainForm.GEN04_VALUE.value), "*") ) {
					if( !confirm(MainForm.GEN04_MSG.value)){
						return false;
					} else {
						warningFg3 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN04_FUTOGO.value == 'LT'){		
				if( warningFg3 == false && suryo > 0 && genka_vl <  demicalFloat(floor(MainForm["kaku_gentanka_vl_" + i].value, 0), Number(MainForm.GEN04_VALUE.value), "*") ) {
					if( !confirm(MainForm.GEN04_MSG.value)){
						return false;
					} else {
						warningFg3 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN04_FUTOGO.value == 'LE'){
				if( warningFg3 == false && suryo > 0 && genka_vl <=  demicalFloat(floor(MainForm["kaku_gentanka_vl_" + i].value, 0), Number(MainForm.GEN04_VALUE.value), "*") ) {
					if( !confirm(MainForm.GEN04_MSG.value)){
						return false;
					} else {
						warningFg3 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN04_FUTOGO.value == 'GE'){
				if( warningFg3 == false && suryo > 0 && genka_vl >=  demicalFloat(floor(MainForm["kaku_gentanka_vl_" + i].value, 0), Number(MainForm.GEN04_VALUE.value), "*") ) {
					if( !confirm(MainForm.GEN04_MSG.value)){
						return false;
					} else {
						warningFg3 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}else if(MainForm.GEN04_FUTOGO.value == 'GT'){
				if( warningFg3 == false && suryo > 0 && genka_vl >  demicalFloat(floor(MainForm["kaku_gentanka_vl_" + i].value, 0), Number(MainForm.GEN04_VALUE.value), "*") ) {
					if( !confirm(MainForm.GEN04_MSG.value)){
						return false;
					} else {
						warningFg3 = true;	// ２回目以降は警告を表示しない。
					}
				}
			}
			
		}
		return true;
	}
	
	<%-- 更新項目変更チェック用関数 --%>
	function modified() {
	
		<%-- すでに変更があれば終了。 --%>
		if( document.MainForm.Modified.value == '1' ) {
			return;
		}
	
		// 実納品日
		if( MainForm.term_zitu_nohin_dt.value != MainForm.term_zitu_nohin_dt.defaultValue ) {
			MainForm.Modified.value = '1';
			return;
		}
	
		for( var i = 0; i < <%= jutyuTorokuBh.getMaxRows() %>; i++ ) {
	
			var obj1 = MainForm.all("kakutei_qt_" + i);
			var obj2 = MainForm.all("kakutei_suryo_qt_" + i);
			var obj3 = MainForm.all("kaku_santi_cd_" + i);
			var obj4 = MainForm.all("kaku_tokaikyu_cd_" + i);
			var obj5 = MainForm.all("kaku_kikaku_cd_" + i);
			var obj6 = MainForm.all("kaku_gentanka_vl_" + i);
			var obj7 = MainForm.all("kaku_baitanka_vl_" + i);
	
			<%-- 訂正後納品数 --%>
			if( obj1 != null && obj1.value != obj1.defaultValue ) {
				MainForm.Modified.value = '1';
				return;
			}
			<%-- 訂正後納品数量 --%>
			if( obj2 != null && obj2.value != obj2.defaultValue ) {
				MainForm.Modified.value = '1';
				return;
			}
			<%-- 産地コード --%>
			if( obj3 != null && obj3.value != obj3.defaultValue ) {
				MainForm.Modified.value = '1';
				return;
			}
			<%-- 等級コード --%>
			if( obj4 != null && obj4.value != obj4.defaultValue ) {
				MainForm.Modified.value = '1';
				return;
			}
			<%-- 規格コード --%>
			if( obj5 != null && obj5.value != obj5.defaultValue ) {
				MainForm.Modified.value = '1';
				return;
			}
		<%	if( tankaChgFg == true ) {	%>		
				<%-- 原単価 --%>
				if( obj6 != null && obj6.value != obj6.defaultValue ) {
					MainForm.Modified.value = '1';
					return;
				}
				<%-- 売単価 --%>
				if( obj7 != null && obj7.value != obj7.defaultValue ) {
					MainForm.Modified.value = '1';
					return;
				}
		<%	}	%>		
			
		}
	}
	
	<%-- 検索条件変更チェック用関数 --%>
	function modifiedCondition() { }
	
	<%-- 確定発注数入力後の入力チェック＆確定発注数量の計算＆原価金額合計、売価金額合計の計算 --%>
	function kakuteiSuCheck(kakuteiSuElement, kakuteiSuryoElement, hachuQt, irisu, kanAvgWt, isTeikan) {
	
		// 明細部確定数チェック処理ON（確定数量へのフォーカス処理で利用）
		meisaiKakuteiQtChekFg = "true";
		
		// 半角整数であるか？
		if( isTextCheck("訂正後納品数", kakuteiSuElement, 1, 0, 0) == false || isLengthCheck("訂正後納品数", kakuteiSuElement, 4, 0) == false ) {
			return;
		}
	
		// 訂正後納品数量計算。
		if( !(kakuteiSuElement.value == "") ) {
			if( isTeikan == true ) {
				if( eval(kakuteiSuElement.value) == 0 ) {
					kakuteiSuElement.value = "0";
					kakuteiSuryoElement.value = "0.000";
				} else {
					kakuteiSuryoElement.value = calcSuryoQtTeikan(kakuteiSuElement.value, irisu);
				}
			} else {
				if( eval(kakuteiSuElement.value) == 0 ) {
					kakuteiSuElement.value = "0";
					kakuteiSuryoElement.value = "0.000";
				} else {
					kakuteiSuryoElement.value = calcSuryoQtTeikan(kakuteiSuElement.value, irisu);
				}
			}
		}
	
		genkaBaikaSumCalc();
		if( !(kakuteiSuElement.value == "") ) {
			kakuteiSuryoElement.value = suryoDecimalLengthSet(kakuteiSuryoElement.value, 2);	
		}
	}
	
	<%-- 明細部確定数フォーカス移動用関数 --%>
	function meisaiKakuteiQtFocusControl(kakuteiQt, kakuteiSuryoQt) {
	
		if(meisaiKakuteiQtChekFg == "true"){
			kakuteiSuryoQt.select();
			kakuteiSuryoQt.focus();		
		} else {
			kakuteiQt.select();
			kakuteiQt.focus();		
		}
		
		meisaiKakuteiQtChekFg = "false";
		
	}
	
	<%-- 確定発注数量入力後の入力チェック＆原価金額合計、売価金額合計の計算(不定貫のみ) --%>
	function kakuteiSuryoCheck(kakuteiSuryoElement) {
	
		// 半角整数であるか？（小数点は可）
		if( isTextCheck('訂正後納品数量', kakuteiSuryoElement, 4, 0, 0) == false || isLengthCheck("納品数量", kakuteiSuryoElement, 5, 2) == false ) {
			return;
		}
	
		genkaBaikaSumCalc();
		if( !(kakuteiSuryoElement.value == "") ) {
			kakuteiSuryoElement.value = suryoDecimalLengthSet(kakuteiSuryoElement.value, 2);	// 小数点以下の桁数調整。
		}
	}
	
	<%-- 原価金額合計、売価金額合計を算出し表示する --%>
	function genkaBaikaSumCalc() {
	
		var genkaSum = 0;
		var genkaWork = 0;
		var baikaSum = 0;
		var baikaWork = 0;
	
		for( i = 0; i < <%= jutyuTorokuBh.getMaxRows() %>; i++ ) {
	
			genkaWork = 0;
			baikaWork = 0;
	
			// １レコードごとの原価金額、売価金額を計算。
			var kakuteiQt = eval( (MainForm.elements['kakutei_qt_' + i].value == "") ? MainForm.elements['hachu_qt_' + i].value : MainForm.elements['kakutei_qt_' + i].value );
			var kakuteiSuryoQt = eval( (MainForm.elements['kakutei_suryo_qt_' + i].value == "") ? MainForm.elements['hachu_suryo_qt_' + i].value : MainForm.elements['kakutei_suryo_qt_' + i].value );
			var genTanTani = eval(MainForm.elements['gen_tan_tani_' + i].value);
			var kakuGentankaVl = eval(MainForm.elements['kaku_gentanka_vl_' + i].value);
			var kakuBaitankaVl = eval(MainForm.elements['kaku_baitanka_vl_' + i].value);
			var teikanKb = MainForm.elements['teikan_kb_' + i].value;
			var baikaFixFg = MainForm.elements['baika_fix_fg_' + i].value;
	
			genkaWork = calcGenkaVl(kakuteiSuryoQt, genTanTani, kakuGentankaVl, teikanKb);
			if( kakuBaitankaVl == null || kakuBaitankaVl == "" ) {
				baikaWork = calcBaikaVl(kakuteiQt, kakuteiSuryoQt, genTanTani, 0, teikanKb, baikaFixFg);
			} else {
				baikaWork = calcBaikaVl(kakuteiQt, kakuteiSuryoQt, genTanTani, kakuBaitankaVl, teikanKb, baikaFixFg);
			}
	
			if( decimalLength(genkaWork) >= 1 ) genkaWork = round(genkaWork, 0); // 小数部は四捨五入。
			if( decimalLength(baikaWork) >= 1 ) baikaWork = round(baikaWork, 0); // 小数部は四捨五入。
			genkaSum += genkaWork;
			baikaSum += baikaWork;
		}
	
		MainForm.genka_kei_vl.value = commaEdit(genkaSum);
		MainForm.baika_kei_vl.value = commaEdit(baikaSum);
	}

</script>
</head>

<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();">
	<form name="MainForm" method="post" action="app">
		<table cellspacing="0" cellpadding="0" border="0" width="100%">
			<tr>
				<td>
					<jsp:include page="ptl000001_Header.jsp?PARAM=納品登録（SSN04003）"></jsp:include>
				</td>
			</tr>
			<tr>
				<td align="center">
					<jsp:include page="rbs00000_common.jsp" flush="true" />
					<input type="hidden" name="Modified" value="">
					<input type="hidden" name="ModifiedCondition" value="">
					<!------ 基本情報部 START ------>
					<div class="term">
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>取引先</th>
								<td colspan="3">
									<input type="text" name="term_torihikisaki_cd" value="<%= HTMLUtil.toText(jutyuItiranBean.getTorihikisakiCd()) %>" tabindex="-1" readonly />
									<input type="text" name="term_torihikisaki_na" value="<%= HTMLUtil.toText(jutyuItiranBean.getTorihikisakiNa()) %>" tabindex="-1" readonly />
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>伝票番号</th>
								<td>
									<input type="text" name="term_denpyo_nb" value="<%= HTMLUtil.toText(jutyuItiranBean.getDenpyoNbString()) %>" tabindex="-1" readonly />
								</td>
								</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>店舗</th>
								<td>
									<input type="text" name="term_tenpo_cd" value="<%= HTMLUtil.toText(jutyuItiranBean.getTenpoCd()) %>" tabindex="-1" readonly style="ime-mode:disabled;" />
									<input type="text" name="term_tenpo_na" value="<%= HTMLUtil.toText(jutyuItiranBean.getTenpoNa()) %>" tabindex="-1" readonly />
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>納品区分</th>
								<td>
									<input name="term_buturyu_na" type="text" value="<%= HTMLUtil.toLabel(jutyuItiranBean.getButuryuSn()) %>" tabindex="-1" readonly />
								</td>
								</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>発注日</th>
								<td>
									<input type="text" name="term_hachu_dt" value="<%= HTMLUtil.toDate(jutyuItiranBean.getHachuDt(), "yyyy/MM/dd")  %>" tabindex="-1" readonly />
								</td>
								</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>納品予定日</th>
								<td>
									<input type="text" name="term_nohin_dt" value="<%= HTMLUtil.toDate(jutyuItiranBean.getNohinDt(), "yyyy/MM/dd")  %>" tabindex="-1" readonly />
								</td>
								</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>実納品日</th>
								<td>
									<input type="text" name="term_zitu_nohin_dt" value="<%= HTMLUtil.toText(jutyuItiranBean.getZituNohinDtForNohinToroku()) %>" maxlength="<%=SystemControlUtil.getDateLength() %>" <%= jutyuItiranBean.isKakutei() == true ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled;\" " : "style=\"ime-mode:disabled;\"" %> />
									<%	if( jutyuItiranBean.isKakutei() == false ) {	%>
									<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.term_zitu_nohin_dt);" />
									<small>（YYYYMMDD）</small>
									<%	}	%>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>便</th>
								<td>
									<input type="text" name="term_bin_nm" value="<%= HTMLUtil.toText(jutyuItiranBean.getBinNm(), "#") %>" tabindex="-1" readonly />
								</td>
							</tr>
						</table>
					</div>
					<!------ 基本情報部 END ------>
					<div class="term">
						<table class="term_btn_area">
							<tr>
								<td align="center">
									<input type="button" value="戻&emsp;る" class="controlButton" onClick="returnCommand();" />
								</td>
							</tr>
						</table>
					</div>
					<br>
					<div class="term_msg_area">
						<jsp:include page="InfoStringMdWare.jsp" />
					</div>
					<input type="hidden" name="GEN01_FUTOGO" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getFutogo() : "" %>">
					<input type="hidden" name="GEN01_VALUE" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getValueString() : "" %>">
					<input type="hidden" name="GEN01_MSG" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getClMsg() : "" %>">
					<input type="hidden" name="GEN02_FUTOGO" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getFutogo() : "" %>">
					<input type="hidden" name="GEN02_VALUE" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getValueString() : "" %>">
					<input type="hidden" name="GEN02_MSG" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getClMsg() : "" %>">
					<input type="hidden" name="GEN03_FUTOGO" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getFutogo() : "" %>">
					<input type="hidden" name="GEN03_VALUE" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getValueString() : "" %>">
					<input type="hidden" name="GEN03_MSG" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getClMsg() : "" %>">
					<input type="hidden" name="GEN04_FUTOGO" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getFutogo() : "" %>">
					<input type="hidden" name="GEN04_VALUE" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getValueString() : "" %>">
					<input type="hidden" name="GEN04_MSG" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getClMsg() : "" %>">
					<input type="hidden" name="onlineDt" value="<%=SystemControlUtil.getOnlineDate()%>" />
					<%	if( jutyuTorokuBh.getMaxRows() > 0 ) {	%>
					<br>
					<div class="list1" align="left">
						<table class="list1_search" cellpadding="0" cellspacing="0">
							<tr>
								<th class="list1_no" align="center"></th>
								<th class="list1_syohin_info" align="center">
									<table border="0" cellspacing="0" cellpadding="0" class="none_border">
											<tr>
												<th class="list1_syohin_cd">商品コード</th>
												<th class="list1_iritsu">入数</th>
												<th class="list1_hachi_tani_na">発単</th>
											</tr>
											<tr>
												<th class="list1_syohin_na" colspan="3">商品名</th>
											</tr>
									</table>
								</th>
								<th class="list1_hachu_qt" align="center">発注数</th>
								<th class="list1_hachu_suryo_qt" align="center">発注数量</th>
								<th class="list1_kakutei_qt" align="center">訂正後納品数</th>
								<th class="list1_kakutei_suryo" align="center">訂正後納品数量</th>
								<th class="list1_gentanka" align="center">原単価</th>
								<th class="list1_baitanka" align="center">売単価</th>
								<th class="list1_santi" align="center">産地</th>
								<th class="list1_tokaikyu" align="center">等級</th>
								<th class="list1_kikaku" align="center">規格</th>
							</tr>
						</table>
						<div id="result_scroll" align="left">
							<table border="0" cellspacing="0" cellpadding="0" class="list1_search">
								<%
								// 明細表示
								int i = 0;
								BigDecimal genkaKeiVl = new BigDecimal("0");
								BigDecimal baikaKeiVl = new BigDecimal("0");
								for (Iterator ite = jutyuTorokuBh.getBeanIterator(); ite.hasNext(); i++) {
									NohinTorokuBean bean = (NohinTorokuBean) ite.next();
									genkaKeiVl = genkaKeiVl.add(new BigDecimal(bean.getKakuGenkaVlString()));
									baikaKeiVl = baikaKeiVl.add(new BigDecimal(bean.getKakuBaikaVlString()));
								%>
								<tr>
									<input type="hidden" name="ido_gentanka_vl_<%=i%>" value="<%=HTMLUtil.toText(bean.getIdoGentankaVl(), "0.00")%>" />
									<input type="hidden" name="ido_baitanka_vl_<%=i%>" value="<%=HTMLUtil.toText(bean.getIdoBaitankaVl(), "#")%>" />
									<input type="hidden" name="isTeikan_<%=i%>" value="<%=bean.isTeikan()%>" />
									<%-- データ伝票行番号 --%>
									<td class="list1_no"><%=HTMLUtil.toLabel(bean.getDataDenpgyoNbString())%></td>
									<td class="list1_syohin_info">
										<table border="0" cellspacing="0" cellpadding="0" class="none_border">
											<tr>
												<td class="list1_syohin_cd">
													<input type="text" value="<%=HTMLUtil.toLabel(bean.getHachuSyohinCd())%>" tabindex="-1" readonly />
												</td>
												<td class="list1_iritsu">
													<input type="text" value="<%=HTMLUtil.toLabel(bean.getIrisuQtString(), "#,##0")%>" tabindex="-1" readonly />
												</td>
												<td class="list1_hachi_tani_na">
													<input type="text" value="<%=HTMLUtil.toLabel(bean.getHachuTaniNa())%>" tabindex="-1" readonly />
												</td>
											</tr>
											<tr>
												<td class="list1_syohin_na" colspan="3">
													<input type="text" value="<%=HTMLUtil.toLabel(bean.getSyohinNa())%>" tabindex="-1" readonly />
												</td>
											</tr>
										</table>
									</td>
									<%-- 発注数 --%>
									<td class="list1_hachu_qt" align="center"><input type="text" value="<%=HTMLUtil.toLabel(bean.getHachuQtString(), "#,##0")%>" tabindex="-1" readonly /></td>
									<%-- 発注数量 --%>
									<td class="list1_hachu_suryo_qt" align="center"><input type="text" value="<%=HTMLUtil.toLabel(bean.getHachuSuryoQtString(), "#,##0.##")%>" tabindex="-1" readonly /></td>
									<%-- 訂正後納品数 --%>
									<td class="list1_kakutei_qt" align="center">
										<input type="hidden" name="hachu_qt_<%=i%>" value="<%=bean.getHachuQt()%>" />
										<input type="hidden" name="hachu_suryo_qt_<%=i%>" value="<%=bean.getHachuSuryoQt()%>" />
										<input type="hidden" name="baika_fix_fg_<%=i%>" value="<%=bean.getBaikaFixFg()%>" />
										<input type="hidden" name="gen_tan_tani_<%=i%>" value="<%=bean.getGenTanTani()%>" />
										<input type="hidden" name="teikan_kb_<%=i%>" value="<%=bean.getTeikanKb()%>" />
										<input type="text" name="kakutei_qt_<%= i %>" 		value="<%= HTMLUtil.toText(bean.getKakuteiQtForText(), "0") %>" maxlength="4" <%= (jutyuItiranBean.isKakutei() || bean.isKakutei()) ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled; width: 35px;\"" : "style=\"ime-mode:disabled;\" onBlur=\"kakuteiSuCheck(this, MainForm.kakutei_suryo_qt_" + i + ", " + bean.getHachuQt() + ", " + bean.getIrisuQt() + ", " + bean.getKanAvgWt() + ", " + bean.isTeikan() + ");\"" %> />										<%-- 納品数量へのフォーカス制御の為のダミー --%>
										<input type="button" style="width:0px; border-width: 0px;" onFocus="meisaiKakuteiQtFocusControl(kakutei_qt_<%=i%>, kakutei_suryo_qt_<%=i%>);" <%=(jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || bean.isTeikan() == true) ? "tabindex=\"-1\"" : ""%> readonly>
									</td>
									<%-- 訂正後納品数量 --%>
									<td class="list1_kakutei_suryo" align="center">
										<input type="text" name="kakutei_suryo_qt_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuteiSuryoQtForText(), "0.##") %>" maxlength="9"
										<%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || bean.isTeikan() == true ) ? "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"ime-mode:disabled;\"" : "onBlur=\"kakuteiSuryoCheck(this);\" style=\"ime-mode:disabled; width: 62px;\"" %> />
									</td>
									<%-- 原単価 --%>
									<td class="list1_gentanka" align="center"><input type="text" name="kaku_gentanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuGentankaVl(), "0.00") %>" maxlength="<%=SystemControlUtil.getGentankaLength() %>" <%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || tankaChgFg == false ) ?  "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"width: 70px;\"" : "style=\"\" onBlur=\"genkaBaikaSumCalc();\"" %>/></td>
									<%-- 売単価 --%>
									<td class="list1_baitanka" align="center"><input type="text" name="kaku_baitanka_vl_<%= i %>" value="<%= HTMLUtil.toText(bean.getKakuBaitankaVlStringForDisp(), "#") %>" maxlength="<%=SystemControlUtil.getBaitankaLength() %>" <%= ( jutyuItiranBean.isKakutei() == true || bean.isKakutei() == true || tankaChgFg == false ) ?  "id=\"no_input_text\" tabindex=\"-1\" readonly style=\"width: 50px;\"" : "style=\"\" onBlur=\"genkaBaikaSumCalc();\"" %>/></td>
									<%-- 産地 --%>
									<td class="list1_santi" align="center">
										<input type="hidden" name="kaku_santi_cd_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuSantiCd())%>" />
										<input type="text" name="kaku_santi_na_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuSantiNa())%>" tabindex="-1" readonly />
										<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
										<input type="button" value="選択" class="detailButton" onClick="pop_santiSel('MainForm.kaku_santi_cd_<%=i%>','MainForm.kaku_santi_na_<%=i%>','<%=jutyuItiranBean.getTorihikisakiCd()%>', MainForm.onlineDt.value)" />
										<%			} else {	%>
										<img src="./images/spacer.gif" align="absmiddle" />
										<%			}	%>
									</td>
									<%-- 等級 --%>
									<td class="list1_tokaikyu" align="center">
										<input type="hidden" name="kaku_tokaikyu_cd_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuTokaikyuCd())%>" />
										<input type="text" name="kaku_tokaikyu_na_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuTokaikyuNa())%>" tabindex="-1" readonly />
										<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
										<input type="button" value="選択" class="detailButton" onClick="pop_tokaiqSel('MainForm.kaku_tokaikyu_cd_<%=i%>','MainForm.kaku_tokaikyu_na_<%=i%>','<%=jutyuItiranBean.getTorihikisakiCd()%>', MainForm.onlineDt.value)" />
										<%			} else {	%>
										<img src="./images/spacer.gif" align="absmiddle" />
										<%			}	%>
									</td>
									<%-- 規格 --%>
									<td class="list1_kikaku" align="center">
										<input type="hidden" name="kaku_kikaku_cd_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuKikakuCd())%>" />
										<input type="text" name="kaku_kikaku_na_<%=i%>" value="<%=HTMLUtil.toText(bean.getKakuKikakuNa())%>" tabindex="-1" readonly />
										<%			if( jutyuItiranBean.isKakutei() == false && bean.isKakutei() == false && jutyuItiranBean.isSoba() == true ) {	%>
										<input type="button" value="選択" class="detailButton" onClick="pop_kikakuSel('MainForm.kaku_kikaku_cd_<%=i%>','MainForm.kaku_kikaku_na_<%=i%>','<%=jutyuItiranBean.getTorihikisakiCd()%>', MainForm.onlineDt.value)" />
										<%			} else {	%>
										<img src="./images/spacer.gif" align="absmiddle" />
										<%			}	%>
									</td>
								</tr>
								<%		}	%>
							</table>
						</div>
					</div>
					<br>
					<div class="list1">
						<table border="0" cellspacing="0" cellpadding="0" align="right">
							<tbody>
								<tr>
									<td align="right">
										<table border="0" cellspacing="1" cellpadding="0" class="list1_update" align="left">
											<tbody>
												<tr>
													<th class="list1_genka_kei">原価金額合計</th>
													<td class="list1_genka_kei">
														<input type="text" name="genka_kei_vl" value="<%=HTMLUtil.toText(genkaKeiVl.doubleValue(), "#,##0")%>" tabindex="-1" readonly />
													</td>
												</tr>
											</tbody>
										</table>
										<table border="0" cellspacing="1" cellpadding="0" class="list1_update" align="left">
											<tbody>
												<tr>
													<th class="list1_baika_kei">売価金額合計</th>
													<td class="list1_baika_kei" align="center">
														<%
															if (baikaKeiVl.doubleValue() == 0D) { // 売価金額合計が０の場合、非表示
														%>
														<input type="text" name="baika_kei_vl" value="" tabindex="-1" readonly />
														<%
															} else {
														%>
														<input type="text" name="baika_kei_vl" value="<%=HTMLUtil.toText(baikaKeiVl, "#,###")%>" tabindex="-1" readonly />
														<%		}	%>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
					<%	}	%>
					<br />
					<div class="list1">
						<table class="list1_btn_area_update">
							<tr>
								<td align="center">
									<%	if( jutyuTorokuBh.getMaxRows() > 0 && jutyuItiranBean.isKakutei() == false ) {	%>
									<input type="button" value="&nbsp;登&nbsp;録&nbsp;" class="controlButton" onClick="updateCommand();" />
									<%	} else {	%>
									<input type="button" value="&nbsp;登&nbsp;録&nbsp;" class="controlButton" disabled />
									<%	}	%>
								</td>
							</tr>
						</table>
					</div>
				</td>
			</tr>
		</table>
	</form>
</body>
</html>
